package com.gumtree.web.seller.service.threatmetrix;

import com.gumtree.common.properties.Env;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.GumtreeCookieProperty;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookieCutter;
import com.gumtree.web.reporting.threatmetrix.ThreatMetrixTracking;
import com.netflix.config.ConfigurationManager;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Properties;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNotSame;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ThreatMetrixServiceTest {

    static {
        // Set up environment configuration for tests
        Properties properties = new Properties();
        properties.setProperty("gumtree.env", Env.PROD.name());
        properties.setProperty(GumtreeCookieProperty.COOKIES_SECURE.getPropertyName(), "false");
        properties.setProperty(GumtreeCookieProperty.COOKIES_DOMAIN.getPropertyName(), "gumtree.com");
        ConfigurationManager.loadProperties(properties);

    }

    @Mock
    private CookieResolver cookieResolver;

    @Mock
    private ThreatMetrixCookieCutter threatMetrixCookieCutter;

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpServletResponse response;

    @Mock
    private ThreatMetrixCookie threatMetrixCookie;


    private ThreatMetrixService threatMetrixService;

    private static final String TEST_ORG_ID = "test-org-123";
    private static final String TEST_WEB_BASE_URL = "https://test.gumtree.com";

    @Before
    public void setUp() {
        threatMetrixService = new ThreatMetrixService();
        ReflectionTestUtils.setField(threatMetrixService, "cookieResolver", cookieResolver);
        ReflectionTestUtils.setField(threatMetrixService, "threatMetrixCookieCutter", threatMetrixCookieCutter);
        ReflectionTestUtils.setField(threatMetrixService, "organisationId", TEST_ORG_ID);
        ReflectionTestUtils.setField(threatMetrixService, "webBaseUrl", TEST_WEB_BASE_URL);
        ReflectionTestUtils.setField(threatMetrixService, "enabled", true);

        // 确保 threatMetrixCookieCutter.getName() 能正常工作
        // 方案1: 直接 mock getName 方法的返回值，避免调用 getBaseName()
        // 这样可以绕过 getBaseName() 可能为 null 的问题
    }

    @Test
    public void testProcessThreatMetrixForApiResponse_WhenDisabled() {
        // Given
        ReflectionTestUtils.setField(threatMetrixService, "enabled", false);

        // When
        ThreatMetrixInfo result = threatMetrixService.processThreatMetrixForApiResponse(request, response);

        // Then
        assertNotNull(result);
        assertFalse(result.isEnabled());
        assertFalse(result.hasValidData());
        assertNull(result.getSessionId());
        assertNull(result.getTracking());

    }

    @Test
    public void testGetExistingThreatMetrixCookie_Success() {
        // Given - 使用真实的 ThreatMetrixCookie 实例
        String testDomain = "gumtree.com";
        ThreatMetrixCookieCutter realCookieCutter = new ThreatMetrixCookieCutter(testDomain);
        ThreatMetrixCookie realThreatMetrixCookie = realCookieCutter.cutNew();

        when(cookieResolver.resolve(request, ThreatMetrixCookie.class)).thenReturn(realThreatMetrixCookie);

        // When
        ThreatMetrixCookie result = threatMetrixService.getExistingThreatMetrixCookie(request);

        // Then
        assertNotNull(result);
        assertEquals(realThreatMetrixCookie, result);
        assertNotNull(result.getDefaultValue()); // 真实 cookie 有有效的 UUID
    }

    @Test
    public void testGetExistingThreatMetrixCookie_Exception() {
        // Given
        when(cookieResolver.resolve(request, ThreatMetrixCookie.class)).thenThrow(new RuntimeException("Cookie not found"));

        // When
        ThreatMetrixCookie result = threatMetrixService.getExistingThreatMetrixCookie(request);

        // Then
        assertNull(result);
    }

    @Test
    public void testIsEnabled() {
        // When/Then
        assertTrue(threatMetrixService.isEnabled());

        // Given disabled
        ReflectionTestUtils.setField(threatMetrixService, "enabled", false);

        // When/Then
        assertFalse(threatMetrixService.isEnabled());
    }

    @Test
    public void testGetOrganisationId() {
        // When/Then
        assertEquals(TEST_ORG_ID, threatMetrixService.getOrganisationId());
    }

    @Test
    public void testGetWebBaseUrl() {
        // When/Then
        assertEquals(TEST_WEB_BASE_URL, threatMetrixService.getWebBaseUrl());
    }

    @Test
    public void testProcessThreatMetrixForApiResponse_WhenEnabled_Success() {
        // Given - 使用真实对象避免 null 问题
        String testDomain = "gumtree.com";
        ThreatMetrixCookieCutter realCookieCutter = new ThreatMetrixCookieCutter(testDomain);
        ReflectionTestUtils.setField(threatMetrixService, "threatMetrixCookieCutter", realCookieCutter);

        // 创建真实的 ThreatMetrixCookie 实例
        ThreatMetrixCookie realThreatMetrixCookie = realCookieCutter.cutNew();
        when(cookieResolver.resolve(request, ThreatMetrixCookie.class)).thenReturn(realThreatMetrixCookie);

        // When
        ThreatMetrixInfo result = threatMetrixService.processThreatMetrixForApiResponse(request, response);

        // Then
        assertNotNull(result);
        assertTrue(result.isEnabled());
        assertTrue(result.hasValidData());
        assertNotNull(result.getSessionId()); // 真实 cookie 生成的 UUID
        assertNotNull(result.getTracking());
        assertNotNull(result.getCookie());
        assertEquals(realThreatMetrixCookie, result.getCookie());

        // Verify tracking object properties
        ThreatMetrixTracking tracking = result.getTracking();
        assertEquals(TEST_ORG_ID, tracking.getOrgId());
        assertEquals(realThreatMetrixCookie.getDefaultValue(), tracking.getSessionId());
        assertEquals(TEST_WEB_BASE_URL, tracking.getWebBaseUrl());

        // Verify cookie was added to response
        verify(response).addCookie(any(Cookie.class));
    }

    @Test
    public void testProcessThreatMetrixForApiResponse_WhenEnabled_WithEnvironmentAwareCookieName() {
        // Given - 使用真实对象测试环境相关的 cookie 命名
        String testDomain = "gumtree.com";
        ThreatMetrixCookieCutter realCookieCutter = new ThreatMetrixCookieCutter(testDomain);
        ReflectionTestUtils.setField(threatMetrixService, "threatMetrixCookieCutter", realCookieCutter);

        // 创建真实的 ThreatMetrixCookie 实例
        ThreatMetrixCookie realThreatMetrixCookie = realCookieCutter.cutNew();
        when(cookieResolver.resolve(request, ThreatMetrixCookie.class)).thenReturn(realThreatMetrixCookie);

        // When
        ThreatMetrixInfo result = threatMetrixService.processThreatMetrixForApiResponse(request, response);

        // Then
        assertNotNull(result);
        assertTrue(result.isEnabled());
        assertTrue(result.hasValidData());
        assertNotNull(result.getSessionId());
        assertEquals(realThreatMetrixCookie, result.getCookie());

        // Verify cookie was added to response (环境相关的命名由真实的 CookieCutter 处理)
        verify(response).addCookie(any(Cookie.class));
    }

    @Test
    public void testProcessThreatMetrixForApiResponse_WhenEnabled_CookieResolverException() {
        // Given
        when(cookieResolver.resolve(request, ThreatMetrixCookie.class))
                .thenThrow(new RuntimeException("Cookie resolution failed"));

        // When/Then - Should propagate the exception since this is the main business logic
        try {
            threatMetrixService.processThreatMetrixForApiResponse(request, response);
        } catch (RuntimeException e) {
            assertEquals("Cookie resolution failed", e.getMessage());
        }
    }

    @Test
    public void testProcessThreatMetrixForApiResponse_WhenEnabled_NullSessionId() {
        // Given - 使用 mock cookie 来模拟 null session ID 场景
        String testDomain = "gumtree.com";
        ThreatMetrixCookieCutter realCookieCutter = new ThreatMetrixCookieCutter(testDomain);
        ReflectionTestUtils.setField(threatMetrixService, "threatMetrixCookieCutter", realCookieCutter);

        // 对于这个特殊测试场景，我们需要 mock cookie 来返回 null
        when(cookieResolver.resolve(request, ThreatMetrixCookie.class)).thenReturn(threatMetrixCookie);
        when(threatMetrixCookie.getDefaultValue()).thenReturn(null);
        when(threatMetrixCookie.getDomain()).thenReturn(testDomain);
        when(threatMetrixCookie.getPath()).thenReturn("/");
        when(threatMetrixCookie.getMaxAge()).thenReturn(-1);
        when(threatMetrixCookie.isHttpOnly()).thenReturn(true);

        // When
        ThreatMetrixInfo result = threatMetrixService.processThreatMetrixForApiResponse(request, response);

        // Then
        assertNotNull(result);
        assertTrue(result.isEnabled());
        assertTrue(result.hasValidData()); // Still valid because cookie and tracking exist
        assertNull(result.getSessionId());
        assertNotNull(result.getTracking());
        assertNotNull(result.getCookie());

        // Verify tracking object with null session ID
        ThreatMetrixTracking tracking = result.getTracking();
        assertEquals(TEST_ORG_ID, tracking.getOrgId());
        assertNull(tracking.getSessionId());
        assertEquals(TEST_WEB_BASE_URL, tracking.getWebBaseUrl());

        // Verify cookie was still added to response
        verify(response).addCookie(any(Cookie.class));
    }

    @Test
    public void testProcessThreatMetrixForApiResponse_WhenEnabled_EmptySessionId() {
        // Given - 使用 mock cookie 来模拟空 session ID 场景
        String testDomain = "gumtree.com";
        String emptySessionId = "";
        ThreatMetrixCookieCutter realCookieCutter = new ThreatMetrixCookieCutter(testDomain);
        ReflectionTestUtils.setField(threatMetrixService, "threatMetrixCookieCutter", realCookieCutter);

        // 对于这个特殊测试场景，我们需要 mock cookie 来返回空字符串
        when(cookieResolver.resolve(request, ThreatMetrixCookie.class)).thenReturn(threatMetrixCookie);
        when(threatMetrixCookie.getDefaultValue()).thenReturn(emptySessionId);
        when(threatMetrixCookie.getDomain()).thenReturn(testDomain);
        when(threatMetrixCookie.getPath()).thenReturn("/");
        when(threatMetrixCookie.getMaxAge()).thenReturn(-1);
        when(threatMetrixCookie.isHttpOnly()).thenReturn(true);

        // When
        ThreatMetrixInfo result = threatMetrixService.processThreatMetrixForApiResponse(request, response);

        // Then
        assertNotNull(result);
        assertTrue(result.isEnabled());
        assertTrue(result.hasValidData());
        assertEquals(emptySessionId, result.getSessionId());
        assertNotNull(result.getTracking());

        // Verify tracking object with empty session ID
        ThreatMetrixTracking tracking = result.getTracking();
        assertEquals(TEST_ORG_ID, tracking.getOrgId());
        assertEquals(emptySessionId, tracking.getSessionId());
        assertEquals(TEST_WEB_BASE_URL, tracking.getWebBaseUrl());

        // Verify cookie was added to response
        verify(response).addCookie(any(Cookie.class));
    }

    @Test
    public void testProcessThreatMetrixForApiResponse_VerifyAllInteractions() {
        // Given - 使用真实对象验证所有交互
        String testDomain = "gumtree.com";
        ThreatMetrixCookieCutter realCookieCutter = new ThreatMetrixCookieCutter(testDomain);
        ReflectionTestUtils.setField(threatMetrixService, "threatMetrixCookieCutter", realCookieCutter);

        // 创建真实的 ThreatMetrixCookie 实例
        ThreatMetrixCookie realThreatMetrixCookie = realCookieCutter.cutNew();
        when(cookieResolver.resolve(request, ThreatMetrixCookie.class)).thenReturn(realThreatMetrixCookie);

        // When
        ThreatMetrixInfo result = threatMetrixService.processThreatMetrixForApiResponse(request, response);

        // Then
        assertNotNull(result);

        // Verify expected interactions occurred
        verify(cookieResolver, times(1)).resolve(request, ThreatMetrixCookie.class);
        verify(response, times(1)).addCookie(any(Cookie.class));

        // Verify the result contains all expected data
        assertTrue(result.isEnabled());
        assertTrue(result.hasValidData());
        assertEquals(realThreatMetrixCookie, result.getCookie());
        assertNotNull(result.getTracking());
        assertNotNull(result.getSessionId());
    }

    @Test
    public void testProcessThreatMetrixForApiResponse_WhenEnabled_SpecificEnvironments() {
        // Given - 使用真实对象测试特定环境
        String testDomain = "gumtree.com";
        ThreatMetrixCookieCutter realCookieCutter = new ThreatMetrixCookieCutter(testDomain);
        ReflectionTestUtils.setField(threatMetrixService, "threatMetrixCookieCutter", realCookieCutter);

        // 创建真实的 ThreatMetrixCookie 实例
        ThreatMetrixCookie realThreatMetrixCookie = realCookieCutter.cutNew();
        when(cookieResolver.resolve(request, ThreatMetrixCookie.class)).thenReturn(realThreatMetrixCookie);

        // When
        ThreatMetrixInfo result = threatMetrixService.processThreatMetrixForApiResponse(request, response);

        // Then
        assertNotNull(result);
        assertTrue(result.isEnabled());
        assertTrue(result.hasValidData());
        assertNotNull(result.getSessionId());
        verify(response).addCookie(any(Cookie.class));
    }


    @Test
    public void testInvalidateThreatMetrixCookie_WhenCookieExists() {
        // Given - 使用真实的 ThreatMetrixCookieCutter 对象
        String testDomain = "gumtree.com";
        ThreatMetrixCookieCutter realCookieCutter = new ThreatMetrixCookieCutter(testDomain);
        ReflectionTestUtils.setField(threatMetrixService, "threatMetrixCookieCutter", realCookieCutter);

        String environmentAwareCookieName = realCookieCutter.getName(Env.PROD); // "gt_tm" for PROD
        Cookie existingCookie = new Cookie(environmentAwareCookieName, "123-234-345");
        existingCookie.setDomain("bixi.gumtree.io"); // 模拟实际的domain
        Cookie[] cookies = {existingCookie};

        when(request.getCookies()).thenReturn(cookies);

        // When
        threatMetrixService.invalidateThreatMetrixCookie(request, response);

        // Then
        ArgumentCaptor<Cookie> cookieCaptor = ArgumentCaptor.forClass(Cookie.class);
        verify(response).addCookie(cookieCaptor.capture());

        Cookie expiredCookie = cookieCaptor.getValue();
        // 验证创建了新的过期Cookie，而不是修改原有Cookie
        assertNotSame(existingCookie, expiredCookie);
        assertEquals(environmentAwareCookieName, expiredCookie.getName());
        assertEquals("", expiredCookie.getValue()); // 过期Cookie的值应该是空字符串
        assertEquals(0, expiredCookie.getMaxAge()); // 关键：maxAge应该是0
        assertEquals("/", expiredCookie.getPath());
        assertEquals("bixi.gumtree.io", expiredCookie.getDomain()); // 关键修复：使用原始cookie的domain
        assertTrue(expiredCookie.isHttpOnly());
    }


    public void testInvalidateThreatMetrixCookie_WhenCookieExistsWithoutDomain() {
        // Given - 测试host-only cookie的情况（没有domain属性）
        String testDomain = "gumtree.com";
        ThreatMetrixCookieCutter realCookieCutter = new ThreatMetrixCookieCutter(testDomain);
        ReflectionTestUtils.setField(threatMetrixService, "threatMetrixCookieCutter", realCookieCutter);

        String environmentAwareCookieName = realCookieCutter.getName(Env.PROD);
        Cookie existingCookie = new Cookie(environmentAwareCookieName, "host-only-cookie-value");
        // 不设置domain，模拟host-only cookie
        Cookie[] cookies = {existingCookie};

        when(request.getCookies()).thenReturn(cookies);

        // When
        threatMetrixService.invalidateThreatMetrixCookie(request, response);

        // Then
        ArgumentCaptor<Cookie> cookieCaptor = ArgumentCaptor.forClass(Cookie.class);
        verify(response).addCookie(cookieCaptor.capture());

        Cookie expiredCookie = cookieCaptor.getValue();
        assertEquals(environmentAwareCookieName, expiredCookie.getName());
        assertEquals("", expiredCookie.getValue());
        assertEquals(0, expiredCookie.getMaxAge());
        assertEquals("/", expiredCookie.getPath());
        assertNull(expiredCookie.getDomain()); // host-only cookie应该没有domain
        assertTrue(expiredCookie.isHttpOnly());
    }

    @Test
    public void testInvalidateThreatMetrixCookie_WhenCookieDoesNotExist() {
        // Given - 使用真实的 ThreatMetrixCookieCutter 对象
        String testDomain = "gumtree.com";
        ThreatMetrixCookieCutter realCookieCutter = new ThreatMetrixCookieCutter(testDomain);
        ReflectionTestUtils.setField(threatMetrixService, "threatMetrixCookieCutter", realCookieCutter);

        Cookie[] cookies = {}; // 空的Cookie数组，模拟没有Cookie的情况

        when(request.getCookies()).thenReturn(cookies);

        // When
        threatMetrixService.invalidateThreatMetrixCookie(request, response);

        // Then
        verify(response, never()).addCookie(any(Cookie.class));
    }

    @Test
    public void testInvalidateThreatMetrixCookie_WhenDisabled() {
        // Given
        ReflectionTestUtils.setField(threatMetrixService, "enabled", false);

        // When
        threatMetrixService.invalidateThreatMetrixCookie(request, response);

        // Then
        verify(response, never()).addCookie(any(Cookie.class));
        verify(request, never()).getCookies();
    }
}